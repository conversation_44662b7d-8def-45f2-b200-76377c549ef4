{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ResetPasswordComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38)(2, \"div\", 39);\n    i0.ɵɵelement(3, \"i\", 40)(4, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42)(6, \"p\", 43);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction ResetPasswordComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 38)(2, \"div\", 45);\n    i0.ɵɵelement(3, \"i\", 46)(4, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42)(6, \"p\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nexport class ResetPasswordComponent {\n  constructor(fb, authService, router, route) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.message = '';\n    this.error = '';\n    this.resetForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      code: ['', [Validators.required, Validators.minLength(6)]],\n      newPassword: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Check if email is provided in query parameters\n    this.route.queryParams.subscribe(params => {\n      if (params['email']) {\n        this.resetForm.patchValue({\n          email: params['email']\n        });\n      }\n    });\n  }\n  onSubmit() {\n    if (this.resetForm.invalid) return;\n    this.authService.resetPassword(this.resetForm.value).subscribe({\n      next: res => {\n        this.message = res.message + ' Redirecting to login...';\n        this.error = '';\n        setTimeout(() => this.router.navigate(['/login']), 1500);\n      },\n      error: err => {\n        this.error = err.error.message || 'Reset failed';\n        this.message = '';\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ResetPasswordComponent_Factory(t) {\n      return new (t || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResetPasswordComponent,\n      selectors: [[\"app-reset-password\"]],\n      decls: 65,\n      vars: 4,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-envelope\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"fas\", \"fa-key\", \"mr-1.5\", \"text-xs\"], [\"type\", \"text\", \"formControlName\", \"code\", \"placeholder\", \"123456\", \"maxlength\", \"6\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"fas\", \"fa-lock\", \"mr-1.5\", \"text-xs\"], [\"type\", \"password\", \"formControlName\", \"newPassword\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-key\", \"mr-2\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/login\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-arrow-left\", \"mr-1.5\", \"text-xs\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"]],\n      template: function ResetPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n          i0.ɵɵtext(23, \" Reset Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 13);\n          i0.ɵɵtext(25, \" Enter your new password \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function ResetPasswordComponent_Template_form_ngSubmit_27_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n          i0.ɵɵelement(30, \"i\", 18);\n          i0.ɵɵtext(31, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 19);\n          i0.ɵɵelement(33, \"input\", 20);\n          i0.ɵɵelementStart(34, \"div\", 21);\n          i0.ɵɵelement(35, \"div\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 16)(37, \"label\", 17);\n          i0.ɵɵelement(38, \"i\", 23);\n          i0.ɵɵtext(39, \" Reset Code \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 19);\n          i0.ɵɵelement(41, \"input\", 24);\n          i0.ɵɵelementStart(42, \"div\", 21);\n          i0.ɵɵelement(43, \"div\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 16)(45, \"label\", 17);\n          i0.ɵɵelement(46, \"i\", 25);\n          i0.ɵɵtext(47, \" New Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 19);\n          i0.ɵɵelement(49, \"input\", 26);\n          i0.ɵɵelementStart(50, \"div\", 21);\n          i0.ɵɵelement(51, \"div\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(52, ResetPasswordComponent_div_52_Template, 8, 1, \"div\", 27);\n          i0.ɵɵtemplate(53, ResetPasswordComponent_div_53_Template, 8, 1, \"div\", 28);\n          i0.ɵɵelementStart(54, \"button\", 29);\n          i0.ɵɵelement(55, \"div\", 30)(56, \"div\", 31);\n          i0.ɵɵelementStart(57, \"span\", 32);\n          i0.ɵɵelement(58, \"i\", 33);\n          i0.ɵɵtext(59, \" Reset Password \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 34)(61, \"div\")(62, \"a\", 35);\n          i0.ɵɵelement(63, \"i\", 36);\n          i0.ɵɵtext(64, \" Back to Login \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"formGroup\", ctx.resetForm);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.resetForm.invalid);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXNldC1wYXNzd29yZC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmVzZXQtcGFzc3dvcmQvcmVzZXQtcGFzc3dvcmQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQUVBLDRLQUE0SyIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ctx_r1", "message", "ResetPasswordComponent", "constructor", "fb", "authService", "router", "route", "resetForm", "group", "email", "required", "code", "<PERSON><PERSON><PERSON><PERSON>", "newPassword", "ngOnInit", "queryParams", "subscribe", "params", "patchValue", "onSubmit", "invalid", "resetPassword", "value", "next", "res", "setTimeout", "navigate", "err", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "ResetPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ResetPasswordComponent_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "ResetPasswordComponent_div_52_Template", "ResetPasswordComponent_div_53_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reset-password\\reset-password.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\reset-password\\reset-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-reset-password',\r\n  templateUrl: './reset-password.component.html',\r\n  styleUrls: ['./reset-password.component.css'],\r\n})\r\nexport class ResetPasswordComponent implements OnInit {\r\n  resetForm: FormGroup;\r\n  message = '';\r\n  error = '';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private route: ActivatedRoute\r\n  ) {\r\n    this.resetForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      code: ['', [Validators.required, Validators.minLength(6)]],\r\n      newPassword: ['', [Validators.required, Validators.minLength(6)]],\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    // Check if email is provided in query parameters\r\n    this.route.queryParams.subscribe(params => {\r\n      if (params['email']) {\r\n        this.resetForm.patchValue({\r\n          email: params['email']\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.resetForm.invalid) return;\r\n\r\n    this.authService.resetPassword(this.resetForm.value).subscribe({\r\n      next: (res: any) => {\r\n        this.message = res.message + ' Redirecting to login...';\r\n        this.error = '';\r\n        setTimeout(() => this.router.navigate(['/login']), 1500);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error.message || 'Reset failed';\r\n        this.message = '';\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen flex items-center justify-center relative\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"w-full max-w-md relative z-10\">\r\n    <div\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative top border with gradient and glow -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <!-- Header -->\r\n      <div class=\"p-6 text-center\">\r\n        <h1\r\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n        >\r\n          Reset Password\r\n        </h1>\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n          Enter your new password\r\n        </p>\r\n      </div>\r\n\r\n      <!-- Form Section -->\r\n      <div class=\"p-6\">\r\n        <form [formGroup]=\"resetForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-5\">\r\n          <!-- Email -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-envelope mr-1.5 text-xs\"></i>\r\n              Email\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                type=\"email\"\r\n                formControlName=\"email\"\r\n                placeholder=\"<EMAIL>\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Reset Code -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-key mr-1.5 text-xs\"></i>\r\n              Reset Code\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                type=\"text\"\r\n                formControlName=\"code\"\r\n                placeholder=\"123456\"\r\n                maxlength=\"6\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- New Password -->\r\n          <div class=\"group\">\r\n            <label\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-lock mr-1.5 text-xs\"></i>\r\n              New Password\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                type=\"password\"\r\n                formControlName=\"newPassword\"\r\n                placeholder=\"••••••••\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Error Message -->\r\n          <div\r\n            *ngIf=\"error\"\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-exclamation-triangle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n                  {{ error }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Success Message -->\r\n          <div\r\n            *ngIf=\"message\"\r\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-check-circle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\r\n                  {{ message }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <button\r\n            type=\"submit\"\r\n            class=\"w-full relative overflow-hidden group mt-6\"\r\n            [disabled]=\"resetForm.invalid\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105 disabled:opacity-50\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300 disabled:opacity-0\"\r\n            ></div>\r\n            <span\r\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\r\n            >\r\n              <i class=\"fas fa-key mr-2\"></i>\r\n              Reset Password\r\n            </span>\r\n          </button>\r\n\r\n          <!-- Back Link -->\r\n          <div\r\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\r\n          >\r\n            <div>\r\n              <a\r\n                routerLink=\"/login\"\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium flex items-center justify-center\"\r\n              >\r\n                <i class=\"fas fa-arrow-left mr-1.5 text-xs\"></i>\r\n                Back to Login\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICqIzDC,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,OAAA,MACF;;;ADpKhB,OAAM,MAAOC,sBAAsB;EAKjCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAPf,KAAAN,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IAQR,IAAI,CAACS,SAAS,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACoB,KAAK,CAAC,CAAC;MACpDE,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACuB,SAAS,CAAC,CAAC,CAAC,CAAC;KACjE,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,KAAK,CAACS,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,OAAO,CAAC,EAAE;QACnB,IAAI,CAACV,SAAS,CAACW,UAAU,CAAC;UACxBT,KAAK,EAAEQ,MAAM,CAAC,OAAO;SACtB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACZ,SAAS,CAACa,OAAO,EAAE;IAE5B,IAAI,CAAChB,WAAW,CAACiB,aAAa,CAAC,IAAI,CAACd,SAAS,CAACe,KAAK,CAAC,CAACN,SAAS,CAAC;MAC7DO,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACxB,OAAO,GAAGwB,GAAG,CAACxB,OAAO,GAAG,0BAA0B;QACvD,IAAI,CAACF,KAAK,GAAG,EAAE;QACf2B,UAAU,CAAC,MAAM,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;MAC1D,CAAC;MACD5B,KAAK,EAAG6B,GAAG,IAAI;QACb,IAAI,CAAC7B,KAAK,GAAG6B,GAAG,CAAC7B,KAAK,CAACE,OAAO,IAAI,cAAc;QAChD,IAAI,CAACA,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACJ;;;uBA3CWC,sBAAsB,EAAAX,EAAA,CAAAsC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxC,EAAA,CAAAsC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1C,EAAA,CAAAsC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA5C,EAAA,CAAAsC,iBAAA,CAAAK,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAAtBlC,sBAAsB;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVnCpD,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,cAA2C;UAKvCD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAA6B;UAIzBD,EAAA,CAAAI,MAAA,wBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAI,MAAA,iCACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,eAAiB;UACeD,EAAA,CAAAsD,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAAxB,QAAA,EAAU;UAAA,EAAC;UAEnD7B,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAA8C;UAC9CF,EAAA,CAAAI,MAAA,eACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAKE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAAyC;UACzCF,EAAA,CAAAI,MAAA,oBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAME;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAmB;UAIfD,EAAA,CAAAE,SAAA,aAA0C;UAC1CF,EAAA,CAAAI,MAAA,sBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAKE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAwD,UAAA,KAAAC,sCAAA,kBAoBM;UAGNzD,EAAA,CAAAwD,UAAA,KAAAE,sCAAA,kBAoBM;UAGN1D,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAE,SAAA,aAA+B;UAC/BF,EAAA,CAAAI,MAAA,wBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAITH,EAAA,CAAAC,cAAA,eAEC;UAMKD,EAAA,CAAAE,SAAA,aAAgD;UAChDF,EAAA,CAAAI,MAAA,uBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;UA1JJH,EAAA,CAAAK,SAAA,IAAuB;UAAvBL,EAAA,CAAA2D,UAAA,cAAAN,GAAA,CAAApC,SAAA,CAAuB;UA+ExBjB,EAAA,CAAAK,SAAA,IAAW;UAAXL,EAAA,CAAA2D,UAAA,SAAAN,GAAA,CAAA7C,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA2D,UAAA,SAAAN,GAAA,CAAA3C,OAAA,CAAa;UAyBdV,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAA2D,UAAA,aAAAN,GAAA,CAAApC,SAAA,CAAAa,OAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}