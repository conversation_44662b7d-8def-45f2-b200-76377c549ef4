const express = require("express");
const router = express.Router();
const { auth, authorizeRoles } = require("../middlewares/auth");
const { resendCode } = require("../controllers/authController");
const { getEmailPreview } = require("../utils/sendEmail");

const { uploadImage } = require("../middlewares/upload");

const {
  signup,
  login,
  getProfile,
  updateProfile,
  changePassword,
  verifyEmail,
  forgotPassword,
  resetPassword,
} = require("../controllers/authController");

router.post("/signup", signup);
router.post("/login", login);
router.get("/profile", auth, getProfile);
router.put("/update-profile", auth, uploadImage.single("image"), updateProfile);
router.put("/change-password", auth, changePassword);
router.post("/verify-email", verifyEmail);
router.post("/forgot-password", forgotPassword);
router.post("/reset-password", resetPassword);
router.post("/resend-code", resendCode);

// Email preview routes (for development/testing)
router.get("/email-preview/:type", (req, res) => {
  const { type } = req.params;
  const { code, userName } = req.query;

  if (!["verification", "reset", "welcome"].includes(type)) {
    return res.status(400).json({ message: "Invalid email type" });
  }

  const html = getEmailPreview(type, code, userName);
  res.send(html);
});

// EXAMPLE: Only admins can access this
router.get("/admin-area", auth, authorizeRoles("admin"), (req, res) => {
  res.json({ message: "Welcome Admin!" });
});

module.exports = router;
