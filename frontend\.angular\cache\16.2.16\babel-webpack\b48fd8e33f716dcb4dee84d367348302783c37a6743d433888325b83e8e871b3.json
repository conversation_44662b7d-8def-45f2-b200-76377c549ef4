{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ForgotPasswordComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37);\n    i0.ɵɵelement(3, \"i\", 38)(4, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"p\", 41);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ForgotPasswordComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 36)(2, \"div\", 43);\n    i0.ɵɵelement(3, \"i\", 44)(4, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"p\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nexport class ForgotPasswordComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.message = '';\n    this.error = '';\n    this.forgotForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n  onSubmit() {\n    if (this.forgotForm.invalid) return;\n    const email = this.forgotForm.value.email;\n    this.authService.forgotPassword(email).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        setTimeout(() => this.router.navigate(['/reset-password'], {\n          queryParams: {\n            email: email\n          }\n        }), 1500);\n      },\n      error: err => {\n        this.error = err.error.message || 'Something went wrong.';\n        this.message = '';\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n      return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordComponent,\n      selectors: [[\"app-forgot-password\"]],\n      decls: 50,\n      vars: 5,\n      consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [\"for\", \"email\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-envelope\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-paper-plane\", \"mr-2\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/login\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1.5\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"]],\n      template: function ForgotPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n          i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n          i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n          i0.ɵɵtext(23, \" Forgot Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 13);\n          i0.ɵɵtext(25, \" Enter your email to receive a reset code \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_27_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n          i0.ɵɵelement(30, \"i\", 18);\n          i0.ɵɵtext(31, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 19);\n          i0.ɵɵelement(33, \"input\", 20);\n          i0.ɵɵelementStart(34, \"div\", 21);\n          i0.ɵɵelement(35, \"div\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, ForgotPasswordComponent_div_36_Template, 3, 0, \"div\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, ForgotPasswordComponent_div_37_Template, 8, 1, \"div\", 24);\n          i0.ɵɵtemplate(38, ForgotPasswordComponent_div_38_Template, 8, 1, \"div\", 25);\n          i0.ɵɵelementStart(39, \"button\", 26);\n          i0.ɵɵelement(40, \"div\", 27)(41, \"div\", 28);\n          i0.ɵɵelementStart(42, \"span\", 29);\n          i0.ɵɵelement(43, \"i\", 30);\n          i0.ɵɵtext(44, \" Send Reset Code \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 31)(46, \"div\");\n          i0.ɵɵtext(47, \" Remember your password? \");\n          i0.ɵɵelementStart(48, \"a\", 32);\n          i0.ɵɵtext(49, \" Sign in \");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"formGroup\", ctx.forgotForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.forgotForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.forgotForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.forgotForm.invalid);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJmb3Jnb3QtcGFzc3dvcmQuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZm9yZ290LXBhc3N3b3JkL2ZvcmdvdC1wYXNzd29yZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUEsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ctx_r2", "message", "ForgotPasswordComponent", "constructor", "fb", "authService", "router", "forgotForm", "group", "email", "required", "onSubmit", "invalid", "value", "forgotPassword", "subscribe", "next", "res", "setTimeout", "navigate", "queryParams", "err", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ForgotPasswordComponent_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "ForgotPasswordComponent_div_36_Template", "ForgotPasswordComponent_div_37_Template", "ForgotPasswordComponent_div_38_Template", "ɵɵproperty", "tmp_1_0", "get", "touched"], "sources": ["C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\forgot-password\\forgot-password.component.ts", "C:\\Users\\<USER>\\Desktop\\PI WEB\\devBridge\\frontend\\src\\app\\views\\front\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { AuthService } from '../../../services/auth.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-forgot-password',\r\n  templateUrl: './forgot-password.component.html',\r\n  styleUrls: ['./forgot-password.component.css'],\r\n})\r\nexport class ForgotPasswordComponent {\r\n  forgotForm: FormGroup;\r\n  message = '';\r\n  error = '';\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router\r\n  ) {\r\n    this.forgotForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n    });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.forgotForm.invalid) return;\r\n\r\n    const email = this.forgotForm.value.email;\r\n\r\n    this.authService.forgotPassword(email).subscribe({\r\n      next: (res: any) => {\r\n        this.message = res.message;\r\n        this.error = '';\r\n        setTimeout(() => this.router.navigate(['/reset-password'], {\r\n          queryParams: { email: email }\r\n        }), 1500);\r\n      },\r\n      error: (err) => {\r\n        this.error = err.error.message || 'Something went wrong.';\r\n        this.message = '';\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div\r\n  class=\"container-fluid p-4 md:p-6 bg-[#edf1f4] dark:bg-[#121212] min-h-screen flex items-center justify-center relative futuristic-layout\"\r\n>\r\n  <!-- Background decorative elements -->\r\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n    <div\r\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n    <div\r\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\r\n    ></div>\r\n\r\n    <!-- Grid pattern -->\r\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\r\n      <div class=\"h-full grid grid-cols-12\">\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"w-full max-w-md relative z-10\">\r\n    <div\r\n      class=\"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\r\n    >\r\n      <!-- Decorative top border with gradient and glow -->\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad]\"\r\n      ></div>\r\n      <div\r\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] blur-md\"\r\n      ></div>\r\n\r\n      <!-- Header -->\r\n      <div class=\"p-6 text-center\">\r\n        <h1\r\n          class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\r\n        >\r\n          Forgot Password\r\n        </h1>\r\n        <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\r\n          Enter your email to receive a reset code\r\n        </p>\r\n      </div>\r\n\r\n      <!-- Form Section -->\r\n      <div class=\"p-6\">\r\n        <form\r\n          [formGroup]=\"forgotForm\"\r\n          (ngSubmit)=\"onSubmit()\"\r\n          class=\"space-y-5\"\r\n        >\r\n          <!-- Email -->\r\n          <div class=\"group\">\r\n            <label\r\n              for=\"email\"\r\n              class=\"flex items-center text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] mb-2\"\r\n            >\r\n              <i class=\"fas fa-envelope mr-1.5 text-xs\"></i>\r\n              Email\r\n            </label>\r\n            <div class=\"relative\">\r\n              <input\r\n                id=\"email\"\r\n                type=\"email\"\r\n                formControlName=\"email\"\r\n                placeholder=\"<EMAIL>\"\r\n                class=\"w-full px-4 py-2.5 text-sm rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\r\n                required\r\n              />\r\n              <div\r\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\r\n              >\r\n                <div\r\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div\r\n              *ngIf=\"\r\n                forgotForm.get('email')?.invalid &&\r\n                forgotForm.get('email')?.touched\r\n              \"\r\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\"\r\n            >\r\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\r\n              Email is required\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Error Message -->\r\n          <div\r\n            *ngIf=\"error\"\r\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#ff6b69] dark:text-[#ff8785] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-exclamation-triangle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#ff6b69] dark:text-[#ff8785]\">\r\n                  {{ error }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Success Message -->\r\n          <div\r\n            *ngIf=\"message\"\r\n            class=\"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\"\r\n          >\r\n            <div class=\"flex items-start\">\r\n              <div\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] mr-2 text-base relative\"\r\n              >\r\n                <i class=\"fas fa-check-circle\"></i>\r\n                <!-- Glow effect -->\r\n                <div\r\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\r\n                ></div>\r\n              </div>\r\n              <div class=\"flex-1\">\r\n                <p class=\"text-xs text-[#4f5fad] dark:text-[#6d78c9]\">\r\n                  {{ message }}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <button\r\n            type=\"submit\"\r\n            class=\"w-full relative overflow-hidden group mt-6\"\r\n            [disabled]=\"forgotForm.invalid\"\r\n          >\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105 disabled:opacity-50\"\r\n            ></div>\r\n            <div\r\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300 disabled:opacity-0\"\r\n            ></div>\r\n            <span\r\n              class=\"relative flex items-center justify-center text-white font-medium py-2.5 px-4 rounded-lg transition-all z-10\"\r\n            >\r\n              <i class=\"fas fa-paper-plane mr-2\"></i>\r\n              Send Reset Code\r\n            </span>\r\n          </button>\r\n\r\n          <!-- Back Link -->\r\n          <div\r\n            class=\"text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] space-y-2 pt-4\"\r\n          >\r\n            <div>\r\n              Remember your password?\r\n              <a\r\n                routerLink=\"/login\"\r\n                class=\"text-[#4f5fad] dark:text-[#6d78c9] hover:text-[#3d4a85] dark:hover:text-[#4f5fad] transition-colors font-medium\"\r\n              >\r\n                Sign in\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICsFvDC,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAIRJ,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAA2C;IAK7CF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAMNR,EAAA,CAAAC,cAAA,cAGC;IAKKD,EAAA,CAAAE,SAAA,YAAmC;IAKrCF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAoB;IAEhBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAG,MAAA,CAAAC,OAAA,MACF;;;ADlIhB,OAAM,MAAOC,uBAAuB;EAKlCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAL,OAAO,GAAG,EAAE;IACZ,KAAAF,KAAK,GAAG,EAAE;IAOR,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CAAC;MAC9BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACmB,KAAK,CAAC;KACpD,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,UAAU,CAACK,OAAO,EAAE;IAE7B,MAAMH,KAAK,GAAG,IAAI,CAACF,UAAU,CAACM,KAAK,CAACJ,KAAK;IAEzC,IAAI,CAACJ,WAAW,CAACS,cAAc,CAACL,KAAK,CAAC,CAACM,SAAS,CAAC;MAC/CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAChB,OAAO,GAAGgB,GAAG,CAAChB,OAAO;QAC1B,IAAI,CAACF,KAAK,GAAG,EAAE;QACfmB,UAAU,CAAC,MAAM,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;UACzDC,WAAW,EAAE;YAAEX,KAAK,EAAEA;UAAK;SAC5B,CAAC,EAAE,IAAI,CAAC;MACX,CAAC;MACDV,KAAK,EAAGsB,GAAG,IAAI;QACb,IAAI,CAACtB,KAAK,GAAGsB,GAAG,CAACtB,KAAK,CAACE,OAAO,IAAI,uBAAuB;QACzD,IAAI,CAACA,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACJ;;;uBAjCWC,uBAAuB,EAAAX,EAAA,CAAA+B,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjC,EAAA,CAAA+B,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnC,EAAA,CAAA+B,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB1B,uBAAuB;MAAA2B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVpC5C,EAAA,CAAAC,cAAA,aAEC;UAGGD,EAAA,CAAAE,SAAA,aAEO;UAMPF,EAAA,CAAAC,cAAA,aAA4D;UAExDD,EAAA,CAAAE,SAAA,aAAmE;UAWrEF,EAAA,CAAAI,YAAA,EAAM;UAIVJ,EAAA,CAAAC,cAAA,cAA2C;UAKvCD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAA6B;UAIzBD,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAA2D;UACzDD,EAAA,CAAAG,MAAA,kDACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAINJ,EAAA,CAAAC,cAAA,eAAiB;UAGbD,EAAA,CAAA8C,UAAA,sBAAAC,2DAAA;YAAA,OAAYF,GAAA,CAAAzB,QAAA,EAAU;UAAA,EAAC;UAIvBpB,EAAA,CAAAC,cAAA,eAAmB;UAKfD,EAAA,CAAAE,SAAA,aAA8C;UAC9CF,EAAA,CAAAG,MAAA,eACF;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,iBAOE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAgD,UAAA,KAAAC,uCAAA,kBASM;UACRjD,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAgD,UAAA,KAAAE,uCAAA,kBAoBM;UAGNlD,EAAA,CAAAgD,UAAA,KAAAG,uCAAA,kBAoBM;UAGNnD,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAE,SAAA,aAAuC;UACvCF,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAITJ,EAAA,CAAAC,cAAA,eAEC;UAEGD,EAAA,CAAAG,MAAA,iCACA;UAAAH,EAAA,CAAAC,cAAA,aAGC;UACCD,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;;UAvHRJ,EAAA,CAAAK,SAAA,IAAwB;UAAxBL,EAAA,CAAAoD,UAAA,cAAAP,GAAA,CAAA7B,UAAA,CAAwB;UA+BnBhB,EAAA,CAAAK,SAAA,GAGD;UAHCL,EAAA,CAAAoD,UAAA,WAAAC,OAAA,GAAAR,GAAA,CAAA7B,UAAA,CAAAsC,GAAA,4BAAAD,OAAA,CAAAhC,OAAA,OAAAgC,OAAA,GAAAR,GAAA,CAAA7B,UAAA,CAAAsC,GAAA,4BAAAD,OAAA,CAAAE,OAAA,EAGD;UAUDvD,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAoD,UAAA,SAAAP,GAAA,CAAArC,KAAA,CAAW;UAuBXR,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAoD,UAAA,SAAAP,GAAA,CAAAnC,OAAA,CAAa;UAyBdV,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAoD,UAAA,aAAAP,GAAA,CAAA7B,UAAA,CAAAK,OAAA,CAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}